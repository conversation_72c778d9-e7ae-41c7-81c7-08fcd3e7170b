{"info": {"_postman_id": "22373a11-1d80-410f-9e21-02eca93719b7", "name": "<PERSON><PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "38416025"}, "item": [{"name": "Coverage Area", "item": [{"name": "Pricing", "item": [{"name": "Express", "item": [{"name": "Get Pricing Express", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"origin\": 6296,\n    \"destination\": 2091,\n    \"weight\": 322,\n    \"item_value\": \"149990\",\n    \"insurance\":1,\n    \"courier\" : [\"jne\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/v6.1/shipping_price", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "v6.1", "shipping_price"]}}, "response": []}]}, {"name": "Instant", "item": [{"name": "Get Pricing Instant", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"service\" : [\"gosend\", \"borzo\", \"grab_express\"],\n    \"item_price\" : 20000,\n    \"origin\" : {\n        \"lat\" : -7.8032616,\n        \"long\" : 110.350244,\n        \"address\" : \"Wirobrajan, Kota Yogyakarta, Daerah <PERSON> Yogy<PERSON>rta, Indonesia\"\n    },\n    \"destination\" : {\n        \"lat\" : -7.734943400000001,\n        \"long\" : 110.405355,\n        \"address\" : \"<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>wa Yogyakarta, Indonesia\"\n    },\n    \"weight\" : 2,\n    \"vehicle\" : \"motor\",\n    \"timezone\" : \"WIB\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/v4/instant/pricing", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "v4", "instant", "pricing"]}}, "response": []}]}]}, {"name": "Province", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{base_url_sandbox}}/api/mitra/province", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "province"]}}, "response": []}, {"name": "Cities", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"provinsi_id\" : 5\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/city", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "city"]}}, "response": []}, {"name": "Distric", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"kabupaten_id\": 419\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/kecamatan", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "kecamatan"]}}, "response": []}, {"name": "Sub Distric", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"kecamatan_id\": 5779\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/kelurahan", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "k<PERSON><PERSON><PERSON>"]}}, "response": []}, {"name": "Address by <PERSON><PERSON><PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"search\": \"<PERSON><PERSON><PERSON>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/v2/get_address_by_name", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "v2", "get_address_by_name"]}}, "response": []}]}, {"name": "Order", "item": [{"name": "Express", "item": [{"name": "Cancel Order Express", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"status\": true,\n    \"method\": \"cancel_shipment\",\n    \"text\": \"Paket akan dibatalkan dalam 2x24jam\",\n    \"data\": {\n        \"success\": \"pending\",\n        \"date\": \"2024-06-17 02:00:00\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/v3/cancel_shipment?awb=17471724826174&reason=Ga jadi ah", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "v3", "cancel_shipment"], "query": [{"key": "awb", "value": "17471724826174"}, {"key": "reason", "value": "Ga jadi ah"}]}}, "response": []}, {"name": "Track Express Order", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"order_id\": \"ADE-000000020\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/tracking", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "tracking"]}}, "response": []}, {"name": "Create Order Express COD", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"address\" : \"<PERSON>l. Jodipati No.29 Perum <PERSON> Sejahtera\",\n    \"phone\" : \"0813334546789\",\n    \"kecamatan_id\": 548,\n    \"kelurahan_id\" : 31487,\n    \"packages\" : [\n        {\n          \"order_id\": \"ADE-000000027\",\n          \"destination_name\": \"Flag Test\",\n          \"destination_phone\": \"082223323333\",\n          \"destination_address\": \"Jl. Magelang KM 11\",\n          \"destination_kecamatan_id\": 548,\n          \"destination_kelurahan_id\": 31483,\n          \"destination_zipcode\": 55598,\n          \"weight\": 520,\n          \"width\": 8,\n          \"height\": 8,\n          \"length\": 8,\n          \"item_value\": 20000,\n          \"shipping_cost\": 10000,\n          \"service\": \"jne\",\n          \"service_type\": \"CTC23\",\n          \"item_name\": \"TEST Item namee\",\n          \"package_type_id\": 1,\n          \"cod\": 0\n        }\n\n    ],\n    \"name\" : \"Tokotries\",\n    \"zipcode\" : \"55598\",\n    \"schedule\" : \"2024-09-25 16:30:00\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://tdev.kiriminaja.com/api/mitra/v5/request_pickup", "protocol": "https", "host": ["tdev", "kiri<PERSON>ja", "com"], "path": ["api", "mitra", "v5", "request_pickup"]}}, "response": []}, {"name": "Create Order Express Custome COD", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"address\" : \"<PERSON>l. Jodipati No.29 Perum <PERSON> Sejahtera\",\n    \"phone\" : \"0813334546789\",\n    \"kecamatan_id\": 5783,\n    \"kelurahan_id\":31532,\n\n    \"packages\" : [\n        {\n          \"order_id\": \"BDI-00112245127\",\n          \"destination_name\": \"Flag Test\",\n          \"destination_phone\": \"082223323333\",\n          \"destination_address\": \"Jl. Magelang KM 11\",\n          \"destination_kecamatan_id\": 5507,\n          \"destination_kelurahan_id\":65005,\n          \"destination_zipcode\": 50141,\n          \"weight\": 520,\n          \"width\": 8,\n          \"height\": 8,\n          \"length\": 8,\n          \"item_value\": 20000,\n          \"shipping_cost\": 11000,\n          \"service\": \"jnt\",\n          \"service_type\": \"EZ\",\n          \"item_name\": \"TEST Item namee\",\n          \"package_type_id\": 7,\n          \"cod\": 40500\n        }\n\n    ],\n    \"name\" : \"Tokotries\",\n    \"zipcode\" : \"55598\",\n    \"schedule\" : \"2025-02-10 16:30:00\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://tdev.kiriminaja.com/api/mitra/v6.1/request_pickup", "protocol": "https", "host": ["tdev", "kiri<PERSON>ja", "com"], "path": ["api", "mitra", "v6.1", "request_pickup"]}}, "response": []}, {"name": "Create Order Express NON COD", "request": {"auth": {"type": "bearer"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n        \"address\": \"Komp. Colombo Jl. <PERSON>.5, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, D.<PERSON>\",\n        \"phone\": \"081234567891\",\n        \"name\": \"Wahidun Warehouse\",\n        \"kecamatan_id\": 6982,\n        \"kelurahan_id\": 31641,\n        \"latitude\": -7.7964628259985025,\n        \"longitude\": 110.44302463531494,\n        \"platform_name\": \"mitra\",\n        \"schedule\": \"2025-02-04 17:00:00\",\n        \"packages\": [\n            {\n                \"order_id\": \"PCRE-3ac4018\",\n                \"destination_name\": \"Odie\",\n                \"destination_phone\": \"0812345678912\",\n                \"destination_address\": \"Jalan Damai 10\",\n                \"destination_kecamatan_id\": 6983,\n                \"destination_kelurahan_id\": 31409,\n                \"weight\": 205,\n                \"width\": 7,\n                \"length\": 7,\n                \"height\": 25,\n                \"qty\": 1,\n                \"item_value\": 270000,\n                \"shipping_cost\": 5000,\n                \"service\": \"jne\",\n                \"service_type\": \"REG23\",\n                \"cod\": 0,\n                \"package_type_id\": 9,\n                \"item_name\": \"product from Pincare Clinic\",\n                \"drop\": false\n            }\n        ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://tdev.kiriminaja.com/api/mitra/v6.1/request_pickup", "protocol": "https", "host": ["tdev", "kiri<PERSON>ja", "com"], "path": ["api", "mitra", "v6.1", "request_pickup"]}}, "response": []}]}, {"name": "Instant", "item": [{"name": "Create Order Instant", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"service\" : \"grab\",\n    \"service_type\" : \"instant\",\n    \"vehicle\" : \"motor\",\n    \"order_prefix\" : \"BDI\",\n    \"packages\" : [\n        {\n            \"destination_name\" : \"Arfian\",\n            \"destination_phone\" : \"081280045616\",\n            \"destination_lat\" : -7.776192418965594,\n            \"destination_long\" : 110.32505379554323,\n            \"destination_address\" : \"<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Daerah <PERSON>timewa Yogyakarta, Indonesia\",\n            \"destination_address_note\" : \"Tidak Ada Destination\",\n            \"origin_name\" : \"Arfian\",\n            \"origin_phone\" : \"081280045616\",\n            \"origin_lat\" : -7.854584796417944,\n            \"origin_long\" : 110.33115444430031,\n            \"origin_address\" : \"Wirobrajan, Kota Yogyakarta, Daerah Istimewa Yogyakarta, Indonesia\",\n            \"origin_address_note\" : \"Tidak Ada Origin\",\n            \"shipping_price\" : 20000,\n            \"item\" : {\n                \"name\" : \"Barang 1\",\n                \"description\" : \"Barang 1 Description\",\n                \"price\" : 20000,\n                \"weight\" : 100\n            }\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/v4/instant/pickup/request", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "v4", "instant", "pickup", "request"]}}, "response": []}, {"name": "Find New Driver", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"order_id\" : \"NBL2-1725442587223\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/v4/instant/pickup/find-new-driver", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "v4", "instant", "pickup", "find-new-driver"]}}, "response": []}, {"name": "Cancel Instant Order", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{base_url_sandbox}}/api/mitra/v4/instant/pickup/void/NBL2-1726540395735", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "v4", "instant", "pickup", "void", "NBL2-1726540395735"]}}, "response": []}, {"name": "Track Instant Order", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url_sandbox}}/api/mitra/v4/instant/tracking/NBL2-1726540395735", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "v4", "instant", "tracking", "NBL2-1726540395735"]}}, "response": []}]}]}, {"name": "Pickup", "item": [{"name": "Pickup Schedule (Express Only)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/v2/schedules", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "v2", "schedules"]}}, "response": []}]}, {"name": "Payment", "item": [{"name": "Get Payment", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"payment_id\" : \"PT6472194770\"\n}"}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/v2/get_payment", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "v2", "get_payment"]}}, "response": []}]}, {"name": "Courier", "item": [{"name": "Courier Services", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"courier_code\" : \"tiki\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/courier_services", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "courier_services"]}}, "response": []}, {"name": "Courier Group", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url_sandbox}}/api/mitra/couriers_group", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "couriers_group"]}}, "response": []}, {"name": "Courier Detail", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"courier_code\": \"idx\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/courier_services", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "courier_services"]}}, "response": []}, {"name": "Option (Set Preference)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{sandbox_apikey}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"services\": [\"jne\",\"jnt\",\"sicepat\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url_sandbox}}/api/mitra/v3/set_whitelist_services", "host": ["{{base_url_sandbox}}"], "path": ["api", "mitra", "v3", "set_whitelist_services"]}}, "response": []}]}]}